[gd_scene load_steps=3 format=3 uid="uid://3ckate3v614f"]

[sub_resource type="GDScript" id="GDScript_mnbyt"]
resource_name = "depth_of_field"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = {
		\"Disabled\": null,
		\"Low\": RenderingServer.DOF_BLUR_QUALITY_LOW,
		\"Medium\": RenderingServer.DOF_BLUR_QUALITY_MEDIUM,
		\"High\": RenderingServer.DOF_BLUR_QUALITY_HIGH
	}


func _apply_settings() -> void:
	if not apply_in_game_setting(currentValue):
		if currentValue == \"Disabled\":
			return
		
		RenderingServer.camera_attributes_set_dof_blur_quality(
			OPTION_LIST_[currentValue],
			true
		)
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="DepthOfField" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_mnbyt")
DEFAULT_VALUE = "Disabled"
OptionsRef = NodePath("Options")
IDENTIFIER = "DepthOfField"
IS_IN_GAME_SETTING = true

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Depth of Field"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
