[gd_scene load_steps=3 format=3 uid="uid://cpc3p78bpu5e2"]

[ext_resource type="PackedScene" uid="uid://t2dui8ppm3a4" path="res://addons/maaacks_menus_template/base/scenes/credits/scrolling_credits.tscn" id="1_bks1v"]
[ext_resource type="Script" uid="uid://cb7vy0lft0cn4" path="res://resource/core/ui/scenes/credits/scrolling_credits.gd" id="2_acqx3"]

[node name="ScrollingCredits" instance=ExtResource("1_bks1v")]
script = ExtResource("2_acqx3")

[node name="CreditsLabel" parent="ScrollContainer/VBoxContainer" index="1"]
text = "[center][font_size=48]Collaborators[/font_size]

[font_size=32]Role[/font_size]
Person 1  
Person 2  
[url=]Person w/ Link[/url]  

[font_size=48]Sourced[/font_size]
[font_size=32]Asset Type[/font_size]
[font_size=24]Use Case[/font_size]
Author: [url=]Name[/url]  
Source: [url=]Domain : webpage.html[/url]  
License: [url=]License[/url]

[font_size=24]Godot Engine Logo[/font_size]
Author: Andrea Calabró  
Source: [url=https://godotengine.org/press/]godotengine.org : press[/url]  
License: [url=https://github.com/godotengine/godot/blob/master/LOGO_LICENSE.txt]CC BY 4.0 International[/url] 

[font_size=48]Tools[/font_size]
[font_size=24]Godot[/font_size]
[img=80]res:///resource/core/ui/assets/godot_engine_logo/logo_vertical_color_dark.png[/img]  
Author: [url=https://godotengine.org/contact]Juan Linietsky, Ariel Manzur, and contributors[/url]  
Source: [url=https://godotengine.org/]godotengine.org[/url]  
License: [url=https://github.com/godotengine/godot/blob/master/LICENSE.txt]MIT License[/url] 

[font_size=24]Godot Menus Template[/font_size]
[img=80]res:///resource/core/ui/assets/plugin_logo/logo.png[/img]  
Author: [url=https://github.com/Maaack/Godot-Menus-Template/graphs/contributors]Marek Belski and contributors[/url]  
Source: [url=https://github.com/Maaack/Godot-Menus-Template]github: Godot-Menus-Template[/url]  
License: [url=LICENSE.txt]MIT License[/url]  

[font_size=24]Git[/font_size]
[img=80]res:///resource/core/ui/assets/git_logo/Git-Logo-2Color.png[/img]  
Author: [url=https://github.com/torvalds]Linus Torvalds[/url]  
Source: [url=https://git-scm.com/downloads]git-scm.com[/url]  
License: [url=https://opensource.org/licenses/GPL-2.0]GNU General Public License version 2[/url]
[/center]"
attribution_file_path = "res://resource/core/ui/ATTRIBUTION.md"
