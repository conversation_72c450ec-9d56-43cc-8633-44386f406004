[gd_scene load_steps=7 format=3 uid="uid://b3w7qpn4nhmu2"]

[ext_resource type="PackedScene" uid="uid://ha87l3hl643g" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/scaler-sub-elements/resolution_scale.tscn" id="1_4irey"]
[ext_resource type="PackedScene" uid="uid://5dydkttc2fww" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/scaler-sub-elements/fsr_mode.tscn" id="2_w65g1"]
[ext_resource type="PackedScene" uid="uid://bdoaqhvw440eh" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/scaler-sub-elements/fsr_sharpness.tscn" id="3_v8pix"]

[sub_resource type="GDScript" id="GDScript_bn0b0"]
resource_name = "scaler"
script/source = "extends MultiElement


func _display_sub_elements() -> void:
	match currentValue:
		\"Disabled\":
			for element in SUB_ELEMENTS_:
				element.hide()
		\"Bilinear\":
			SUB_ELEMENTS_[0].show()
			SUB_ELEMENTS_[1].hide()
			SUB_ELEMENTS_[2].hide()
		\"FSR 2.2\":
			SUB_ELEMENTS_[0].hide()
			SUB_ELEMENTS_[1].show()
			SUB_ELEMENTS_[2].show()
"

[sub_resource type="GDScript" id="GDScript_mdyud"]
resource_name = "scaling_mode"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = {
		\"Disabled\": Viewport.SCALING_3D_MODE_BILINEAR,
		\"Bilinear\": Viewport.SCALING_3D_MODE_BILINEAR,
		\"FSR 2.2\": Viewport.SCALING_3D_MODE_FSR2
	}


# Loads the saved/default values of the element
func load_settings() -> void:
	super.load_settings()
	# Check if TAA is selected
	call_deferred(\"check_anti_aliasing\")


func option_selected(index: int) -> void:
	super.option_selected(index)
	# Check if TAA is selected
	check_anti_aliasing()


# Called to apply the settings in the settings cache
func _apply_settings() -> void:
	get_viewport().set_scaling_3d_mode(OPTION_LIST_[currentValue])
	if currentValue == \"Disabled\":
		get_viewport().set_scaling_3d_scale(1.0)


# Checks if TAA is selected while FSR 2.2 is enabled
func check_anti_aliasing() -> void:
	if not ParentRef.ELEMENT_REFERENCE_TABLE_.has(\"AntiAliasing\"):
		return
	
	var AntiAliasingRef: SettingsElement =\\
		ParentRef.ELEMENT_REFERENCE_TABLE_[\"AntiAliasing\"]
	var taaIndex: int = AntiAliasingRef.OPTION_LIST_.find(\"TAA\")
	
	if currentValue != \"FSR 2.2\":
		AntiAliasingRef.OptionsRef.set_item_disabled(
			taaIndex,
			false
		)
		return
	
	AntiAliasingRef.OptionsRef.set_item_disabled(
		taaIndex,
		true
	)
	if AntiAliasingRef.currentValue == \"TAA\":
		var disabledIndex: int = AntiAliasingRef.OPTION_LIST_.find(\"Disabled\")
		# Reselect the anti aliasing mode
		AntiAliasingRef.OptionsRef.select(disabledIndex)
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="Scaler" type="VBoxContainer" node_paths=PackedStringArray("MainElementRef", "SUB_ELEMENTS_")]
offset_top = 51.0
offset_right = 1152.0
offset_bottom = 51.0
script = SubResource("GDScript_bn0b0")
MainElementRef = NodePath("ScalingMode")
SUB_ELEMENTS_ = [NodePath("ResolutionScale"), NodePath("FSRMode"), NodePath("FSRSharpness")]

[node name="ScalingMode" type="HBoxContainer" parent="." node_paths=PackedStringArray("OptionsRef")]
layout_mode = 2
size_flags_horizontal = 3
script = SubResource("GDScript_mdyud")
DEFAULT_VALUE = "Disabled"
OptionsRef = NodePath("Options")
IDENTIFIER = "ScalingMode"

[node name="Label" type="Label" parent="ScalingMode"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Scaling Mode"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="ScalingMode"]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")

[node name="ResolutionScale" parent="." instance=ExtResource("1_4irey")]
visible = false
layout_mode = 2
size_flags_vertical = 3

[node name="FSRMode" parent="." instance=ExtResource("2_w65g1")]
visible = false
layout_mode = 2

[node name="FSRSharpness" parent="." instance=ExtResource("3_v8pix")]
visible = false
layout_mode = 2
