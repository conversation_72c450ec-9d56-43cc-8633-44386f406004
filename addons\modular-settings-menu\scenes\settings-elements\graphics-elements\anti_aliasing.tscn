[gd_scene load_steps=3 format=3 uid="uid://csg6c3uuct1ls"]

[sub_resource type="GDScript" id="GDScript_5w56l"]
resource_name = "anti_aliasing"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = [
		\"Disabled\",
		\"FXAA\",
		\"2x MSAA\",
		\"4x MSAA\",
		\"8x MSAA\",
		\"TAA\"
	]


func _apply_settings() -> void:
	# Enables the selected anti aliasing
	match currentValue:
		\"Disabled\":
			set_anti_aliasing_mode()
		\"FXAA\":
			set_anti_aliasing_mode(Viewport.SCREEN_SPACE_AA_FXAA)
		\"2x MSAA\":
			set_anti_aliasing_mode(
				Viewport.SCREEN_SPACE_AA_DISABLED,
				Viewport.MSAA_2X
			)
		\"4x MSAA\":
			set_anti_aliasing_mode(
				Viewport.SCREEN_SPACE_AA_DISABLED,
				Viewport.MSAA_4X
			)
		\"8x MSAA\":
			set_anti_aliasing_mode(
				Viewport.SCREEN_SPACE_AA_DISABLED,
				Viewport.MSAA_8X
			)
		\"TAA\":
			set_anti_aliasing_mode(
				Viewport.SCREEN_SPACE_AA_DISABLED,
				Viewport.MSAA_DISABLED, true
			)


# Sets the anti aliasing mode according to the values provided
func set_anti_aliasing_mode(
	fxaaMode: Viewport.ScreenSpaceAA = Viewport.SCREEN_SPACE_AA_DISABLED,
	msaaMode: Viewport.MSAA = Viewport.MSAA_DISABLED,
	taaMode: bool = false
) -> void:
	get_viewport().set_screen_space_aa(fxaaMode)
	get_viewport().set_msaa_3d(msaaMode)
	get_viewport().set_use_taa(taaMode)
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="AntiAliasing" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_5w56l")
DEFAULT_VALUE = "Disabled"
OptionsRef = NodePath("Options")
IDENTIFIER = "AntiAliasing"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Anti Aliasing"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
