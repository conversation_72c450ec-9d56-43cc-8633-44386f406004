[gd_scene load_steps=3 format=3 uid="uid://cgqiotob5aaoq"]

[sub_resource type="GDScript" id="GDScript_v3ktk"]
resource_name = "shadow_quality"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = {
		\"Low\": RenderingServer.SHADOW_QUALITY_SOFT_LOW,
		\"Medium\": RenderingServer.SHADOW_QUALITY_SOFT_MEDIUM,
		\"High\": RenderingServer.SHADOW_QUALITY_SOFT_HIGH
	}


# Called to apply the settings in the settings cache
func _apply_settings() -> void:
	RenderingServer.directional_soft_shadow_filter_set_quality(
		OPTION_LIST_[currentValue]
	)
	RenderingServer.positional_soft_shadow_filter_set_quality(
		OPTION_LIST_[currentValue]
	)
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="ShadowQuality" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_v3ktk")
DEFAULT_VALUE = "Low"
OptionsRef = NodePath("Options")
IDENTIFIER = "ShadowQuality"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Shadow Quality"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
