[gd_scene load_steps=3 format=3 uid="uid://djk4yvl3pqoys"]

[ext_resource type="PackedScene" uid="uid://b6bl3n5mp3m1e" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/option_control/list_option_control.tscn" id="1_15h7k"]
[ext_resource type="Script" uid="uid://4uk4cr4uvbp8" path="res://resource/core/ui/scenes/menus/options_menu/audio/audio_input_option_control.gd" id="2_3fw24"]

[node name="AudioInputOptionControl" instance=ExtResource("1_15h7k")]
script = ExtResource("2_3fw24")
option_name = "Input Device"
option_section = 2
key = "InputDevice"
section = "AudioSettings"
property_type = 4

[node name="OptionLabel" parent="." index="0"]
text = "Input Device :"

[node name="OptionButton" parent="." index="1"]
size_flags_horizontal = 3
text_overrun_behavior = 1
clip_text = true
