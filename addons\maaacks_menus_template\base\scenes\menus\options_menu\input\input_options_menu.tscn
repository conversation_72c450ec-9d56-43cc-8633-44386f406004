[gd_scene load_steps=7 format=3 uid="uid://dp3rgqaehb3xu"]

[ext_resource type="Script" uid="uid://eborw7q4b07h" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/input/input_options_menu.gd" id="1"]
[ext_resource type="PackedScene" uid="uid://qoexj4ptqt8a" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/input/input_icon_mapper.tscn" id="2_627ul"]
[ext_resource type="Script" uid="uid://1nf36h0gms3q" path="res://addons/maaacks_menus_template/base/scripts/capture_focus.gd" id="2_wft4x"]
[ext_resource type="Script" uid="uid://custha7r0uoic" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/input/key_assignment_dialog.gd" id="3_wsh2h"]
[ext_resource type="PackedScene" uid="uid://bxp45814v6ydv" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/input/input_actions_list.tscn" id="4_lf2nw"]
[ext_resource type="PackedScene" uid="uid://ci6wgl2ngd35n" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/input/input_actions_tree.tscn" id="5_b2whh"]

[node name="Controls" type="MarginContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/margin_left = 32
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 32
theme_override_constants/margin_bottom = 8
script = ExtResource("1")

[node name="InputIconMapper" parent="." instance=ExtResource("2_627ul")]

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("2_wft4x")
search_depth = 5

[node name="InputMappingContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
alignment = 1

[node name="Label" type="Label" parent="VBoxContainer/InputMappingContainer"]
layout_mode = 2
text = "Actions & Inputs"
horizontal_alignment = 1

[node name="InputActionsList" parent="VBoxContainer/InputMappingContainer" node_paths=PackedStringArray("input_icon_mapper") instance=ExtResource("4_lf2nw")]
unique_name_in_owner = true
custom_minimum_size = Vector2(560, 480)
layout_mode = 2
input_icon_mapper = NodePath("../../../InputIconMapper")

[node name="InputActionsTree" parent="VBoxContainer/InputMappingContainer" node_paths=PackedStringArray("input_icon_mapper") instance=ExtResource("5_b2whh")]
unique_name_in_owner = true
visible = false
custom_minimum_size = Vector2(400, 480)
layout_mode = 2
input_icon_mapper = NodePath("../../../InputIconMapper")

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/InputMappingContainer"]
layout_mode = 2
alignment = 1

[node name="ResetButton" type="Button" parent="VBoxContainer/InputMappingContainer/HBoxContainer"]
layout_mode = 2
text = "Reset"

[node name="KeyAssignmentDialog" type="ConfirmationDialog" parent="."]
title = "Assign Key"
size = Vector2i(400, 158)
dialog_text = "


"
script = ExtResource("3_wsh2h")

[node name="VBoxContainer" type="VBoxContainer" parent="KeyAssignmentDialog"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 8.0
offset_top = 8.0
offset_right = -8.0
offset_bottom = -49.0
grow_horizontal = 2
grow_vertical = 2

[node name="InputLabel" type="Label" parent="KeyAssignmentDialog/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "None"
horizontal_alignment = 1

[node name="InputTextEdit" type="TextEdit" parent="KeyAssignmentDialog/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
placeholder_text = "Focus here to assign inputs."
context_menu_enabled = false
shortcut_keys_enabled = false
selecting_enabled = false
deselect_on_focus_loss_enabled = false
drag_and_drop_selection_enabled = false
middle_mouse_paste_enabled = false
caret_move_on_right_click = false

[node name="DelayTimer" type="Timer" parent="KeyAssignmentDialog"]
unique_name_in_owner = true
wait_time = 0.1
one_shot = true

[node name="KeyDeletionDialog" type="ConfirmationDialog" parent="."]
title = "Remove Key"
size = Vector2i(419, 100)
dialog_text = "Are you sure you want to remove KEY from ACTION?"

[node name="OneInputMinimumDialog" type="AcceptDialog" parent="."]
title = "Cannot Remove"
size = Vector2i(398, 100)

[node name="AlreadyAssignedDialog" type="AcceptDialog" parent="."]
title = "Already Assigned"
size = Vector2i(398, 100)

[node name="ResetConfirmationDialog" type="ConfirmationDialog" parent="."]
size = Vector2i(486, 100)
dialog_text = "Are you sure you want to reset controls back to the defaults?"

[connection signal="already_assigned" from="VBoxContainer/InputMappingContainer/InputActionsList" to="." method="_on_input_actions_list_already_assigned"]
[connection signal="button_clicked" from="VBoxContainer/InputMappingContainer/InputActionsList" to="." method="_on_input_actions_list_button_clicked"]
[connection signal="minimum_reached" from="VBoxContainer/InputMappingContainer/InputActionsList" to="." method="_on_input_actions_list_minimum_reached"]
[connection signal="add_button_clicked" from="VBoxContainer/InputMappingContainer/InputActionsTree" to="." method="_on_input_actions_tree_add_button_clicked"]
[connection signal="already_assigned" from="VBoxContainer/InputMappingContainer/InputActionsTree" to="." method="_on_input_actions_tree_already_assigned"]
[connection signal="minimum_reached" from="VBoxContainer/InputMappingContainer/InputActionsTree" to="." method="_on_input_actions_tree_minimum_reached"]
[connection signal="remove_button_clicked" from="VBoxContainer/InputMappingContainer/InputActionsTree" to="." method="_on_input_actions_tree_remove_button_clicked"]
[connection signal="pressed" from="VBoxContainer/InputMappingContainer/HBoxContainer/ResetButton" to="." method="_on_reset_button_pressed"]
[connection signal="confirmed" from="KeyAssignmentDialog" to="." method="_on_key_assignment_dialog_confirmed"]
[connection signal="visibility_changed" from="KeyAssignmentDialog" to="KeyAssignmentDialog" method="_on_visibility_changed"]
[connection signal="focus_entered" from="KeyAssignmentDialog/VBoxContainer/InputTextEdit" to="KeyAssignmentDialog" method="_on_text_edit_focus_entered"]
[connection signal="focus_exited" from="KeyAssignmentDialog/VBoxContainer/InputTextEdit" to="KeyAssignmentDialog" method="_on_input_text_edit_focus_exited"]
[connection signal="gui_input" from="KeyAssignmentDialog/VBoxContainer/InputTextEdit" to="KeyAssignmentDialog" method="_on_input_text_edit_gui_input"]
[connection signal="confirmed" from="KeyDeletionDialog" to="." method="_on_key_deletion_dialog_confirmed"]
[connection signal="confirmed" from="ResetConfirmationDialog" to="." method="_on_reset_confirmation_dialog_confirmed"]
