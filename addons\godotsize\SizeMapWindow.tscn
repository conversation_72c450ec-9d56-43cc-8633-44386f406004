[gd_scene load_steps=9 format=3 uid="uid://cbkdorc6w0dln"]

[ext_resource type="Texture2D" uid="uid://b7e1ks6rtn214" path="res://addons/godotsize/assets/cog.png" id="1_4mbh3"]
[ext_resource type="Texture2D" uid="uid://djku0vshg7sak" path="res://addons/godotsize/assets/reload.png" id="2_3ckyf"]
[ext_resource type="Texture2D" uid="uid://c22owepavswk3" path="res://addons/godotsize/assets/scan.png" id="3_it35i"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_l2i63"]

[sub_resource type="LabelSettings" id="LabelSettings_tv3er"]
font_size = 18

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1vy2d"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uoukc"]
bg_color = Color(0, 0, 0, 0.235294)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
corner_detail = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8l07c"]
bg_color = Color(0.494118, 0.494118, 0.494118, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
corner_detail = 4

[node name="SizeMapWindow" type="AcceptDialog"]
disable_3d = true
title = "Size Map"
initial_position = 2
size = Vector2i(550, 450)
visible = true
min_size = Vector2i(300, 300)

[node name="DelayTimer" type="Timer" parent="."]
wait_time = 0.2
one_shot = true

[node name="Background" type="ColorRect" parent="."]
offset_left = 8.0
offset_top = 8.0
offset_right = 542.0
offset_bottom = 401.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.113725, 0.133333, 0.160784, 1)

[node name="Main" type="VBoxContainer" parent="Background"]
layout_mode = 1
anchors_preset = -1
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 12.0
offset_top = 4.0
offset_right = -12.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2

[node name="HBoxContainer" type="HBoxContainer" parent="Background/Main"]
layout_mode = 2

[node name="OptionsButton" type="Button" parent="Background/Main/HBoxContainer"]
layout_mode = 2
text = "Options"
icon = ExtResource("1_4mbh3")

[node name="ModeNote" type="Label" parent="Background/Main/HBoxContainer"]
visible = false
modulate = Color(1, 1, 1, 0.392157)
layout_mode = 2
size_flags_vertical = 1
tooltip_text = "When this mode is enabled, import data will be scanned instead of the actual files in the project folder. Not all files will show!"
mouse_filter = 0
theme_override_font_sizes/font_size = 12
text = " * Import data mode enabled"
vertical_alignment = 1

[node name="VSeparator" type="VSeparator" parent="Background/Main/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/separator = SubResource("StyleBoxEmpty_l2i63")

[node name="ErrorsButton" type="Button" parent="Background/Main/HBoxContainer"]
visible = false
layout_mode = 2
text = "Errors"

[node name="RescanButton" type="Button" parent="Background/Main/HBoxContainer"]
layout_mode = 2
text = "Rescan"
icon = ExtResource("2_3ckyf")

[node name="HSeparator" type="HSeparator" parent="Background/Main"]
modulate = Color(1, 1, 1, 0.470588)
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="Background/Main"]
layout_mode = 2
size_flags_horizontal = 13
size_flags_vertical = 15

[node name="List" type="VBoxContainer" parent="Background/Main/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ScanningLabel" type="HBoxContainer" parent="Background/Main/ScrollContainer/List"]
modulate = Color(1, 1, 1, 0.627451)
layout_mode = 2
alignment = 1

[node name="TextureRect" type="TextureRect" parent="Background/Main/ScrollContainer/List/ScanningLabel"]
custom_minimum_size = Vector2(22, 28)
layout_mode = 2
texture = ExtResource("3_it35i")
stretch_mode = 5

[node name="Label" type="Label" parent="Background/Main/ScrollContainer/List/ScanningLabel"]
layout_mode = 2
size_flags_vertical = 1
text = "Scanning..."
label_settings = SubResource("LabelSettings_tv3er")

[node name="Item" type="VBoxContainer" parent="Background/Main/ScrollContainer/List"]
visible = false
layout_mode = 2

[node name="Label" type="HBoxContainer" parent="Background/Main/ScrollContainer/List/Item"]
layout_mode = 2

[node name="FileName" type="Label" parent="Background/Main/ScrollContainer/List/Item/Label"]
modulate = Color(1, 1, 1, 0.705882)
layout_mode = 2
size_flags_horizontal = 3
text = "template.png"
vertical_alignment = 1
clip_text = true
text_overrun_behavior = 3

[node name="FileSize" type="Label" parent="Background/Main/ScrollContainer/List/Item/Label"]
modulate = Color(1, 1, 1, 0.392157)
layout_mode = 2
text = "24.8 KB"

[node name="VSeparator" type="VSeparator" parent="Background/Main/ScrollContainer/List/Item/Label"]
layout_mode = 2
theme_override_styles/separator = SubResource("StyleBoxEmpty_1vy2d")

[node name="Percent" type="Label" parent="Background/Main/ScrollContainer/List/Item/Label"]
modulate = Color(1, 1, 1, 0.705882)
layout_mode = 2
text = "33.2%"

[node name="FileSizeBar" type="ProgressBar" parent="Background/Main/ScrollContainer/List/Item"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2
size_flags_vertical = 1
mouse_filter = 1
theme_override_styles/background = SubResource("StyleBoxFlat_uoukc")
theme_override_styles/fill = SubResource("StyleBoxFlat_8l07c")
step = 0.1
show_percentage = false
