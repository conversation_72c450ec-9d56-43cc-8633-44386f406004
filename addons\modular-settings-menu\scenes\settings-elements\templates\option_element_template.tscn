[gd_scene load_steps=3 format=3 uid="uid://dc1yif146sxav"]

[sub_resource type="GDScript" id="GDScript_sk6j2"]
resource_name = "option_element_template"
script/source = "extends OptionElement


# Provide the options for the element
func _init() -> void:
	OPTION_LIST_ = {
		\"Example\": null
	}


# Called to apply the setting to the game
func _apply_settings() -> void:
	pass
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="OptionElementTemplate" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_sk6j2")
DEFAULT_VALUE = "Example"
OptionsRef = NodePath("Options")
IDENTIFIER = "OptionTemplate"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Option Element"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
