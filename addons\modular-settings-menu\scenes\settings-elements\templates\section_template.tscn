[gd_scene load_steps=7 format=3 uid="uid://ejplmui1twn4"]

[ext_resource type="Script" uid="uid://d27da0yf24sse" path="res://addons/modular-settings-menu/scripts/settings_section.gd" id="1_aglse"]
[ext_resource type="PackedScene" uid="uid://dc1yif146sxav" path="res://addons/modular-settings-menu/scenes/settings-elements/templates/option_element_template.tscn" id="2_ws4gk"]
[ext_resource type="PackedScene" uid="uid://csr6kawyf3t3i" path="res://addons/modular-settings-menu/scenes/settings-elements/templates/multi-element-template/multi_element_template.tscn" id="2_xre3j"]
[ext_resource type="PackedScene" uid="uid://bjcuw6amean2" path="res://addons/modular-settings-menu/scenes/settings-elements/templates/slider_element_template.tscn" id="3_o11e6"]
[ext_resource type="PackedScene" uid="uid://cxaxyqer0af21" path="res://addons/modular-settings-menu/scenes/settings-elements/templates/toggle_element_template.tscn" id="4_41mog"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_e0spm"]

[node name="SectionTemplate" type="TabBar"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/tab_focus = SubResource("StyleBoxEmpty_e0spm")
script = ExtResource("1_aglse")
IDENTIFIER = "ExampleSection"

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer"]
layout_mode = 2
horizontal_scroll_mode = 0

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="ElementList" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 24

[node name="ParentElementTemplate" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList" instance=ExtResource("2_xre3j")]
layout_mode = 2

[node name="SubSection" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList"]
layout_mode = 2

[node name="SubSectionName" type="Label" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/SubSection"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Sub Section"

[node name="HSeparator" type="HSeparator" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/SubSection"]
layout_mode = 2

[node name="SubSectionElements" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/SubSection"]
layout_mode = 2

[node name="OptionElementTemplate" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/SubSection/SubSectionElements" instance=ExtResource("2_ws4gk")]
layout_mode = 2

[node name="SliderElementTemplate" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/SubSection/SubSectionElements" instance=ExtResource("3_o11e6")]
layout_mode = 2

[node name="ToggleElementTemplate" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/SubSection/SubSectionElements" instance=ExtResource("4_41mog")]
layout_mode = 2
