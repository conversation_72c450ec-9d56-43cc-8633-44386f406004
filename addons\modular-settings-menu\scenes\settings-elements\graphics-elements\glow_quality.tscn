[gd_scene load_steps=3 format=3 uid="uid://du5urp5d2dyjb"]

[sub_resource type="GDScript" id="GDScript_7hlvr"]
resource_name = "glow_quality"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = [
		\"Disabled\",
		\"Low\",
		\"High\"
	]


func _apply_settings() -> void:
	apply_in_game_setting(currentValue)
	
	if currentValue == \"Disabled\":
		return
	
	# Toggle bicubic upscaling
	match currentValue:
		\"Low\":
			RenderingServer.environment_glow_set_use_bicubic_upscale(false)
		\"High\":
			RenderingServer.environment_glow_set_use_bicubic_upscale(true)
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="GlowQuality" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_7hlvr")
DEFAULT_VALUE = "Disabled"
OptionsRef = NodePath("Options")
IDENTIFIER = "GlowQuality"
IS_IN_GAME_SETTING = true

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Glow Quality"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
