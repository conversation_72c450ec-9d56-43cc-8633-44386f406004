extends Control
class_name LobbyMenu

## Lobby interface for multiplayer games
## Shows connected players, chat, and game settings

# --- UI References ---
@onready var players_label: Label = $HBoxContainer/LeftPanel/PlayersPanel/VBoxContainer/PlayersLabel
@onready var players_list: ItemList = $HBoxContainer/LeftPanel/PlayersPanel/VBoxContainer/PlayersList
@onready var start_game_button: Button = $HBoxContainer/LeftPanel/ButtonsContainer/StartGame
@onready var leave_game_button: Button = $HBoxContainer/LeftPanel/ButtonsContainer/LeaveGame

# --- Game Settings UI ---
@onready var game_mode_option: OptionButton = $HBoxContainer/RightPanel/GameSettingsPanel/VBoxContainer/GameModeContainer/GameModeOption
@onready var score_limit_spinbox: SpinBox = $HBoxContainer/RightPanel/GameSettingsPanel/VBoxContainer/ScoreLimitContainer/ScoreLimitSpinBox
@onready var time_limit_spinbox: SpinBox = $HBoxContainer/RightPanel/GameSettingsPanel/VBoxContainer/TimeLimitContainer/TimeLimitSpinBox
@onready var map_option: OptionButton = $HBoxContainer/RightPanel/GameSettingsPanel/VBoxContainer/MapOption

# --- Chat UI ---
@onready var chat_display: RichTextLabel = $HBoxContainer/RightPanel/ChatPanel/VBoxContainer/ChatDisplay
@onready var chat_input: LineEdit = $HBoxContainer/RightPanel/ChatPanel/VBoxContainer/ChatInput
@onready var status_label: Label = $StatusBar/StatusLabel

# --- State ---
var is_host: bool = false
var connected_players: Dictionary = {}
var chat_messages: Array[Dictionary] = []
var current_settings: Dictionary = {}

# --- Available Maps ---
var available_maps: Array[Dictionary] = [
	{"name": "Default Arena", "path": "res://scenes/game_world.tscn"},
	{"name": "Test Map", "path": "res://scenes/test_map.tscn"},
	{"name": "Internal Heights", "path": "res://maps/internal_heights.tscn"}
]

func _ready() -> void:
	# Determine if we're the host
	is_host = MultiplayerManager.is_hosting
	
	# Connect UI signals
	start_game_button.pressed.connect(_on_start_game_pressed)
	leave_game_button.pressed.connect(_on_leave_game_pressed)
	chat_input.text_submitted.connect(_on_chat_submitted)
	
	# Connect game settings signals (only for host)
	if is_host:
		game_mode_option.item_selected.connect(_on_game_mode_changed)
		score_limit_spinbox.value_changed.connect(_on_score_limit_changed)
		time_limit_spinbox.value_changed.connect(_on_time_limit_changed)
		map_option.item_selected.connect(_on_map_changed)
	else:
		# Disable settings for non-hosts
		_disable_host_controls()
	
	# Connect to multiplayer events
	if MultiplayerManager:
		MultiplayerManager.player_connected.connect(_on_player_connected)
		MultiplayerManager.player_disconnected.connect(_on_player_disconnected)
		MultiplayerManager.game_started.connect(_on_game_started)
	
	# Initialize current settings
	_initialize_settings()
	
	# Load current players
	_refresh_players_list()
	
	# Add welcome message
	_add_chat_message("System", "Welcome to the lobby!", Color.YELLOW)
	
	# Update UI state
	_update_ui_state()

func _initialize_settings() -> void:
	current_settings = {
		"game_mode": GameRulesManager.GameMode.DEATHMATCH,
		"score_limit": 25,
		"time_limit": 5,
		"map_index": 0
	}
	
	# Update UI to match settings
	game_mode_option.selected = current_settings.game_mode
	score_limit_spinbox.value = current_settings.score_limit
	time_limit_spinbox.value = current_settings.time_limit
	map_option.selected = current_settings.map_index

func _disable_host_controls() -> void:
	# Only host can change game settings
	game_mode_option.disabled = true
	score_limit_spinbox.editable = false
	time_limit_spinbox.editable = false
	map_option.disabled = true
	start_game_button.disabled = true

func _update_ui_state() -> void:
	# Update players count
	var player_count = connected_players.size()
	var max_players = 8  # Could be configurable
	players_label.text = "Players (%d/%d)" % [player_count, max_players]
	
	# Update start button (only host can start, need at least 1 player)
	if is_host:
		start_game_button.disabled = player_count < 1
		status_label.text = "Ready to start game" if player_count >= 1 else "Waiting for players..."
	else:
		status_label.text = "Waiting for host to start game..."

# --- Player Management ---

func _refresh_players_list() -> void:
	players_list.clear()
	connected_players = MultiplayerManager.get_connected_players()
	
	print("-- Refreshing lobby player list --")
	print("MultiplayerManager players: ", connected_players)
	print("Local peer ID: ", MultiplayerManager.get_local_peer_id())
	
	for peer_id in connected_players:
		var player_info = connected_players[peer_id]
		var player_name = player_info.get("name", "Player " + str(peer_id))
		var team_id = player_info.get("team", 0)
		print("Processing peer %d: %s (team %d)" % [peer_id, player_name, team_id])
		
		# Add player to list with team info
		var display_text = player_name
		if TeamManager and TeamManager.get_all_teams_info().size() > 1:
			var team_info = TeamManager.get_team_info(team_id)
			display_text += " (" + team_info.name + ")"
		
		# Mark host
		if peer_id == 1:
			display_text += " [HOST]"
		
		players_list.add_item(display_text)
		
		# Set team color if available
		if TeamManager:
			var team_info = TeamManager.get_team_info(team_id)
			players_list.set_item_custom_bg_color(players_list.get_item_count() - 1, team_info.color)

func _on_player_connected(peer_id: int, player_info: Dictionary) -> void:
	print("Player connected to lobby: ", player_info.name)
	_refresh_players_list()
	_update_ui_state()
	
	# Add chat message
	_add_chat_message("System", player_info.name + " joined the lobby", Color.GREEN)

func _on_player_disconnected(peer_id: int) -> void:
	print("Player disconnected from lobby: ", peer_id)
	_refresh_players_list()
	_update_ui_state()
	
	# Add chat message
	_add_chat_message("System", "Player left the lobby", Color.RED)

# --- Game Settings (Host Only) ---

func _on_game_mode_changed(index: int) -> void:
	if not is_host:
		return
	
	current_settings.game_mode = index
	_sync_settings()

func _on_score_limit_changed(value: float) -> void:
	if not is_host:
		return
	
	current_settings.score_limit = int(value)
	_sync_settings()

func _on_time_limit_changed(value: float) -> void:
	if not is_host:
		return
	
	current_settings.time_limit = int(value)
	_sync_settings()

func _on_map_changed(index: int) -> void:
	if not is_host:
		return
	
	current_settings.map_index = index
	_sync_settings()

func _sync_settings() -> void:
	# Sync settings to all clients
	if is_host:
		sync_lobby_settings.rpc(current_settings)

@rpc("authority", "call_local", "reliable")
func sync_lobby_settings(settings: Dictionary) -> void:
	current_settings = settings
	
	# Update UI if not host
	if not is_host:
		game_mode_option.selected = settings.game_mode
		score_limit_spinbox.value = settings.score_limit
		time_limit_spinbox.value = settings.time_limit
		map_option.selected = settings.map_index
	
	# Show settings change in chat
	var mode_names = ["Deathmatch", "Team Deathmatch", "Cooperative"]
	var mode_name = mode_names[settings.game_mode] if settings.game_mode < mode_names.size() else "Unknown"
	var map_name = available_maps[settings.map_index].name if settings.map_index < available_maps.size() else "Unknown"
	
	_add_chat_message("System", "Game settings updated: %s, Score: %d, Time: %d min, Map: %s" % [
		mode_name, settings.score_limit, settings.time_limit, map_name
	], Color.CYAN)

# --- Chat System ---

func _on_chat_submitted(text: String) -> void:
	if text.strip_edges().is_empty():
		return
	
	var player_info = MultiplayerManager.get_player_info(MultiplayerManager.get_local_peer_id())
	var player_name = player_info.get("name", "Player")
	
	# Send chat message to all players
	send_chat_message.rpc(player_name, text.strip_edges())
	
	# Clear input
	chat_input.clear()

@rpc("any_peer", "call_local", "reliable")
func send_chat_message(sender_name: String, message: String) -> void:
	_add_chat_message(sender_name, message, Color.WHITE)

func _add_chat_message(sender: String, message: String, color: Color = Color.WHITE) -> void:
	# Add timestamp
	var time_dict = Time.get_time_dict_from_system()
	var timestamp = "%02d:%02d" % [time_dict.hour, time_dict.minute]
	
	# Format message with BBCode
	var formatted_message = "[color=#%s][%s] %s: %s[/color]" % [
		color.to_html(false),
		timestamp,
		sender,
		message
	]
	
	chat_display.append_text(formatted_message + "\n")
	
	# Store message
	chat_messages.append({
		"sender": sender,
		"message": message,
		"timestamp": timestamp,
		"color": color
	})
	
	# Limit chat history
	if chat_messages.size() > 100:
		chat_messages.pop_front()

# --- Game Control ---

func _on_start_game_pressed() -> void:
	if not is_host:
		return
	
	if connected_players.size() < 1:
		_add_chat_message("System", "Need at least 1 player to start", Color.RED)
		return
	
	# Configure game rules
	var game_mode = current_settings.game_mode as GameRulesManager.GameMode
	var score_limit = current_settings.score_limit
	var time_limit = current_settings.time_limit
	
	# Get selected map
	var map_path = available_maps[current_settings.map_index].path
	
	_add_chat_message("System", "Starting game...", Color.YELLOW)
	
	# Start the game
	MultiplayerManager.start_game(map_path)

func _on_leave_game_pressed() -> void:
	# Disconnect and return to main menu
	MultiplayerManager.disconnect_from_game()

func _on_game_started() -> void:
	# Game is starting, this will be handled by scene change
	print("Game started from lobby")

# --- Input Handling ---

func _input(event: InputEvent) -> void:
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_ENTER, KEY_KP_ENTER:
				# Focus chat input
				if not chat_input.has_focus():
					chat_input.grab_focus()
			KEY_ESCAPE:
				# Focus away from chat
				if chat_input.has_focus():
					chat_input.release_focus()
			KEY_F5:
				# Refresh players list
				_refresh_players_list()

# --- Team Management (Future Enhancement) ---

func _on_player_item_selected(index: int) -> void:
	# Could be used for team management, kicking players, etc.
	if not is_host:
		return
	
	# Get selected player
	if index < 0 or index >= players_list.get_item_count():
		return
	
	# For now, just show player info
	var player_keys = connected_players.keys()
	if index < player_keys.size():
		var peer_id = player_keys[index]
		var player_info = connected_players[peer_id]
		print("Selected player: ", player_info)

# --- Utility Functions ---

func _get_game_mode_name(mode: GameRulesManager.GameMode) -> String:
	match mode:
		GameRulesManager.GameMode.DEATHMATCH:
			return "Deathmatch"
		GameRulesManager.GameMode.TEAM_DEATHMATCH:
			return "Team Deathmatch"
		GameRulesManager.GameMode.COOPERATIVE:
			return "Cooperative"
		_:
			return "Unknown"

# --- Admin Commands (Future Enhancement) ---

func _process_admin_command(command: String) -> void:
	# Could be used for admin commands in chat
	if not is_host:
		return
	
	var parts = command.split(" ")
	if parts.size() == 0:
		return
	
	match parts[0].to_lower():
		"/kick":
			if parts.size() > 1:
				# Kick player by name
				pass
		"/balance":
			# Balance teams
			if TeamManager:
				TeamManager.balance_teams()
				_add_chat_message("System", "Teams have been balanced", Color.CYAN)
		"/settings":
			# Show current settings
			var mode_name = _get_game_mode_name(current_settings.game_mode)
			var map_name = available_maps[current_settings.map_index].name
			_add_chat_message("System", "Current settings: %s, Score: %d, Time: %d min, Map: %s" % [
				mode_name, current_settings.score_limit, current_settings.time_limit, map_name
			], Color.CYAN)