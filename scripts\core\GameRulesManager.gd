extends Node
# GameRulesManager - Autoload Singleton
# Note: No class_name needed for autoload singletons

## Manages game rules, modes, and match flow
## Handles scoring, win conditions, and game state

# --- Game Modes ---
enum GameMode {
	DEATHMATCH,      # Free-for-all
	TEAM_DEATHMATCH, # Team vs team
	COOPERATIVE     # PvE mode
}

# --- Game State ---
var current_mode: GameMode = GameMode.DEATHMATCH
var match_active: bool = false
var game_paused: bool = false
var can_pause: bool = true

func show_pause_menu_ui(show: bool) -> void:
	game_paused = show
	pause_menu_visibility_changed.emit(show)  # Emit signal locally
	if multiplayer.has_multiplayer_peer():
		if multiplayer.is_server():
			show_pause_menu.rpc(show)
		else:
			show_pause_menu.rpc_id(1, show)

func _input(event: InputEvent) -> void:
	if event.is_action_pressed("pause") and match_active:
		if multiplayer.is_server() or not multiplayer.multiplayer_peer:
			show_pause_menu_ui(!game_paused)

@rpc("any_peer", "call_local", "reliable")
func show_pause_menu(show: bool) -> void:
	print("DEBUG: show_pause_menu called with show=", show)
	var pause_menu = get_node_or_null("/root/PauseMenu")
	if not pause_menu:
		print("DEBUG: Creating new pause menu instance")
		pause_menu = load("res://scenes/ui/PauseMenu.tscn").instantiate()
		pause_menu.process_mode = Node.PROCESS_MODE_ALWAYS  # Ensure menu works while paused
		get_tree().root.add_child(pause_menu)
		
		# Manually connect button signals since the scene connections might not work with dynamic instantiation
		var resume_button = pause_menu.get_node("Panel/VBoxContainer/ResumeButton")
		var quit_button = pause_menu.get_node("Panel/VBoxContainer/QuitButton")
		
		if resume_button:
			resume_button.pressed.connect(_on_resume_button_pressed)
			print("DEBUG: Connected resume button")
		else:
			print("ERROR: Resume button not found")
		
		if quit_button:
			quit_button.pressed.connect(_on_quit_button_pressed)
			print("DEBUG: Connected quit button")
		else:
			print("ERROR: Quit button not found")
	
	pause_menu.visible = show
	# Ensure mouse is released when showing menu
	if show:
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	else:
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	print("DEBUG: Pause menu visibility set to ", show, ", mouse mode: ", Input.get_mouse_mode())
var match_time_remaining: float = 300.0  # 5 minutes default
var score_limit: int = 25
var time_limit: float = 300.0

# --- Match Statistics ---
var match_start_time: float
var player_stats: Dictionary = {}  # peer_id -> stats

# --- Timers ---
var match_timer: Timer

# --- Signals ---
signal match_started()
signal match_ended(winner_info: Dictionary)
signal player_scored(peer_id: int, points: int)
signal match_time_updated(time_remaining: float)
signal pause_menu_visibility_changed(show: bool)

func _ready() -> void:
	# Create match timer
	match_timer = Timer.new()
	match_timer.one_shot = true
	match_timer.timeout.connect(_on_match_timeout)
	add_child(match_timer)
	
	# Connect to multiplayer events
	if MultiplayerManager:
		MultiplayerManager.player_connected.connect(_on_player_connected)
		MultiplayerManager.player_disconnected.connect(_on_player_disconnected)

func _process(delta: float) -> void:
	if match_active and match_timer.time_left > 0:
		match_time_remaining = match_timer.time_left
		match_time_updated.emit(match_time_remaining)

# --- Public API ---

## Start a new match with specified mode and settings
func start_match(mode: GameMode = GameMode.DEATHMATCH, time_limit_minutes: float = 5.0, score_limit_value: int = 25) -> void:
	if not MultiplayerManager.is_server():
		print("Only server can start matches")
		return
	
	current_mode = mode
	time_limit = time_limit_minutes * 60.0
	score_limit = score_limit_value
	match_time_remaining = time_limit
	
	# Reset all player stats
	_reset_player_stats()
	
	# Start match timer
	match_timer.start(time_limit)
	match_active = true
	match_start_time = Time.get_unix_time_from_system()
	
	print("Match started: ", GameMode.keys()[mode])
	start_match_for_all.rpc(mode, time_limit, score_limit)

## End the current match
func end_match() -> void:
	if not MultiplayerManager.is_server():
		return
	
	match_active = false
	match_timer.stop()
	
	var winner_info = _calculate_winner()
	print("Match ended. Winner: ", winner_info)
	
	end_match_for_all.rpc(winner_info)

## Handle player death/kill events
func on_player_killed(killer_id: int, victim_id: int) -> void:
	if not MultiplayerManager.is_server():
		return
	
	# Update killer stats
	if killer_id in player_stats:
		player_stats[killer_id]["kills"] += 1
		player_stats[killer_id]["score"] += _get_kill_points()
		
		# Update player info in MultiplayerManager
		var new_score = player_stats[killer_id]["score"]
		MultiplayerManager.update_player_score(killer_id, new_score)
		
		player_scored.emit(killer_id, _get_kill_points())
		
		# Check win condition
		if player_stats[killer_id]["score"] >= score_limit:
			end_match()
	
	# Update victim stats
	if victim_id in player_stats:
		player_stats[victim_id]["deaths"] += 1
	
	# Sync stats to all clients
	sync_player_stats.rpc(player_stats)

## Get current game mode
func get_current_mode() -> GameMode:
	return current_mode

## Get match statistics
func get_match_stats() -> Dictionary:
	return {
		"active": match_active,
		"mode": current_mode,
		"time_remaining": match_time_remaining,
		"score_limit": score_limit,
		"player_stats": player_stats
	}

## Get player statistics
func get_player_stats(peer_id: int) -> Dictionary:
	return player_stats.get(peer_id, {})

# --- RPC Methods ---

@rpc("authority", "call_local", "reliable")
func start_match_for_all(mode: GameMode, time_limit_seconds: float, score_limit_value: int) -> void:
	current_mode = mode
	time_limit = time_limit_seconds
	score_limit = score_limit_value
	match_time_remaining = time_limit
	match_active = true
	
	if match_timer:
		match_timer.start(time_limit)
	
	match_started.emit()

@rpc("authority", "call_local", "reliable")
func end_match_for_all(winner_info: Dictionary) -> void:
	match_active = false
	if match_timer:
		match_timer.stop()
	
	match_ended.emit(winner_info)

@rpc("authority", "call_local", "reliable")
func sync_player_stats(stats: Dictionary) -> void:
	player_stats = stats

# --- Internal Methods ---

func _reset_player_stats() -> void:
	player_stats.clear()
	
	# Initialize stats for all connected players
	for peer_id in MultiplayerManager.get_connected_players():
		_initialize_player_stats(peer_id)

func _initialize_player_stats(peer_id: int) -> void:
	player_stats[peer_id] = {
		"kills": 0,
		"deaths": 0,
		"score": 0,
		"join_time": Time.get_unix_time_from_system()
	}

func _get_kill_points() -> int:
	match current_mode:
		GameMode.DEATHMATCH, GameMode.TEAM_DEATHMATCH:
			return 1
		GameMode.COOPERATIVE:
			return 1
		_:
			return 1

func _calculate_winner() -> Dictionary:
	var winner_info = {
		"type": "none",
		"peer_id": -1,
		"team_id": -1,
		"name": "No Winner",
		"score": 0
	}
	
	match current_mode:
		GameMode.DEATHMATCH:
			# Find player with highest score
			var highest_score = -1
			var winner_peer_id = -1
			
			for peer_id in player_stats:
				if player_stats[peer_id]["score"] > highest_score:
					highest_score = player_stats[peer_id]["score"]
					winner_peer_id = peer_id
			
			if winner_peer_id != -1:
				var player_info = MultiplayerManager.get_player_info(winner_peer_id)
				winner_info = {
					"type": "player",
					"peer_id": winner_peer_id,
					"team_id": -1,
					"name": player_info.get("name", "Player"),
					"score": highest_score
				}
		
		GameMode.TEAM_DEATHMATCH:
			# Calculate team scores and find winning team
			var team_scores = {}
			
			for peer_id in player_stats:
				var player_info = MultiplayerManager.get_player_info(peer_id)
				var team_id = player_info.get("team", 0)
				
				if not team_id in team_scores:
					team_scores[team_id] = 0
				team_scores[team_id] += player_stats[peer_id]["score"]
			
			# Find winning team
			var highest_team_score = -1
			var winning_team = -1
			
			for team_id in team_scores:
				if team_scores[team_id] > highest_team_score:
					highest_team_score = team_scores[team_id]
					winning_team = team_id
			
			if winning_team != -1:
				winner_info = {
					"type": "team",
					"peer_id": -1,
					"team_id": winning_team,
					"name": "Team " + str(winning_team),
					"score": highest_team_score
				}
	
	return winner_info

# --- Signal Handlers ---

func _on_match_timeout() -> void:
	print("Match time expired")
	end_match()

func _on_player_connected(peer_id: int, player_info: Dictionary) -> void:
	# Initialize stats for new player
	_initialize_player_stats(peer_id)
	
	# If match is active, sync current state to new player
	if match_active:
		sync_player_stats.rpc_id(peer_id, player_stats)

func _on_player_disconnected(peer_id: int) -> void:
	# Keep stats for potential reconnection
	# Could add logic here to handle mid-match disconnections
	pass

# --- Utility Methods ---

## Check if match is currently active
func is_match_active() -> bool:
	return match_active

## Get formatted time remaining string
func get_time_remaining_formatted() -> String:
	var minutes = int(match_time_remaining) / 60
	var seconds = int(match_time_remaining) % 60
	return "%02d:%02d" % [minutes, seconds]

## Get current leaderboard (sorted by score)
func get_leaderboard() -> Array:
	var leaderboard = []
	
	for peer_id in player_stats:
		var player_info = MultiplayerManager.get_player_info(peer_id)
		var stats = player_stats[peer_id]
		
		leaderboard.append({
			"peer_id": peer_id,
			"name": player_info.get("name", "Player"),
			"team": player_info.get("team", 0),
			"score": stats["score"],
			"kills": stats["kills"],
			"deaths": stats["deaths"],
			"kd_ratio": stats["kills"] / float(max(stats["deaths"], 1))
		})
	
	# Sort by score (descending)
	leaderboard.sort_custom(func(a, b): return a["score"] > b["score"])
	
	return leaderboard

# --- Pause Menu Button Handlers ---

func _on_resume_button_pressed():
	print("DEBUG: Resume button pressed in GameRulesManager")
	show_pause_menu_ui(false)

func _on_quit_button_pressed():
	print("DEBUG: Quit button pressed in GameRulesManager")
	# Hide and cleanup the pause menu before disconnecting
	_cleanup_pause_menu()
	# Small delay to ensure menu is cleaned up before scene change
	await get_tree().create_timer(0.1).timeout
	MultiplayerManager.disconnect_from_game()

func _cleanup_pause_menu():
	print("DEBUG: Cleaning up pause menu")
	var pause_menu = get_node_or_null("/root/PauseMenu")
	if pause_menu:
		pause_menu.queue_free()
		print("DEBUG: Pause menu removed")
	# Reset mouse mode
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
