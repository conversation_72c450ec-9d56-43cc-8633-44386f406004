[gd_scene load_steps=4 format=3 uid="uid://tmvujxflhh0p"]

[ext_resource type="PackedScene" uid="uid://cd0jbh4metflb" path="res://addons/maaacks_menus_template/base/scenes/loading_screen/loading_screen.tscn" id="1_yd7sp"]
[ext_resource type="Script" uid="uid://dttocn12k6n4h" path="res://resource/core/ui/scenes/loading_screen/loading_screen_with_shader_caching.gd" id="2_3xb6k"]

[sub_resource type="QuadMesh" id="QuadMesh_klnwy"]

[node name="LoadingScreen" instance=ExtResource("1_yd7sp")]
script = ExtResource("2_3xb6k")
_spatial_shader_material_dir = ""
_cache_shaders_scene = "res://resource/core/ui/scenes/game_scene/game_ui.tscn"
_mesh = SubResource("QuadMesh_klnwy")
_matching_extensions = Array[String]([".tres", ".material", ".res"])
_ignore_subfolders = Array[String]([".", ".."])
_shader_delay_timer = 0.1

[node name="SpatialShaderTypeCaches" type="Node3D" parent="." index="2"]
unique_name_in_owner = true

[node name="Camera3D" type="Camera3D" parent="SpatialShaderTypeCaches" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.408)
current = true
