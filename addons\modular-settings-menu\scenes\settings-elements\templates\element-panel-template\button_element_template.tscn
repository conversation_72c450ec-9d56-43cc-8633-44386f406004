[gd_scene load_steps=3 format=3 uid="uid://d0rkws47yf8l2"]

[ext_resource type="Script" uid="uid://ciiwn8oy1xim" path="res://addons/modular-settings-menu/scripts/base-settings-elements/button_element.gd" id="1_cnjcn"]
[ext_resource type="PackedScene" uid="uid://cwhs0kpipkxfv" path="res://addons/modular-settings-menu/scenes/settings-elements/templates/element-panel-template/element_panel_template.tscn" id="2_gbpfn"]

[node name="ButtonElementTemplate" type="Button"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "Button Element"
script = ExtResource("1_cnjcn")
ElementPanelScene = ExtResource("2_gbpfn")
