[gd_scene load_steps=3 format=3 uid="uid://5dydkttc2fww"]

[sub_resource type="GDScript" id="GDScript_usma2"]
resource_name = "fsr_options"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = {
		\"Ultra Quality\": 0.77,
		\"Quality\": 0.67,
		\"Balanced\": 0.59,
		\"Performance\": 0.5
	}


func _apply_settings() -> void:
	get_viewport().set_scaling_3d_scale(OPTION_LIST_[currentValue])
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="FSRMode" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
size_flags_vertical = 3
script = SubResource("GDScript_usma2")
DEFAULT_VALUE = "Balanced"
OptionsRef = NodePath("Options")
IDENTIFIER = "FSRMode"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "FSR Mode"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
