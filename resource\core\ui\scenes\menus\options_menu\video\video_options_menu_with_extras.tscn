[gd_scene load_steps=4 format=3 uid="uid://c170s2hq0g5j6"]

[ext_resource type="PackedScene" uid="uid://b2numvphf2kau" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/video/video_options_menu.tscn" id="1_0sl50"]
[ext_resource type="Script" uid="uid://dpqjetb60gd8t" path="res://resource/core/ui/scenes/menus/options_menu/video/video_options_menu.gd" id="2_7vula"]
[ext_resource type="PackedScene" uid="uid://b6bl3n5mp3m1e" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/option_control/list_option_control.tscn" id="3_v241i"]

[node name="Video" instance=ExtResource("1_0sl50")]
script = ExtResource("2_7vula")

[node name="AntiAliasingControl" parent="VBoxContainer" index="3" instance=ExtResource("3_v241i")]
layout_mode = 2
lock_titles = true
option_values = [0, 1, 2, 3]
option_titles = Array[String](["Disabled (Fastest)", "2x", "4x", "8x (Slowest)"])
option_name = "Anti-Aliasing"
option_section = 3
key = "Anti-aliasing"
section = "VideoSettings"
property_type = 2
default_value = 0

[node name="CameraShakeControl" parent="VBoxContainer" index="4" instance=ExtResource("3_v241i")]
visible = false
layout_mode = 2
lock_titles = true
option_values = [1.0, 0.75, 0.5, 0.0]
option_titles = Array[String](["Normal", "Reduced", "Minimal", "None"])
option_name = "Camera Shake"
option_section = 3
key = "CameraShake"
section = "VideoSettings"
property_type = 3
default_value = 1.0
