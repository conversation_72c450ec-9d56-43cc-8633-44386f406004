[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://deh4hglelj25g"
path.s3tc="res://.godot/imported/internal_heights_computerwall005a.png-5b91d9ae1df21071a242586d3f4b6b59.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "525c9133b23c7fcb63d4d779cb17d092"
}

[deps]

source_file="res://maps/mesh_source/internal_heights_computerwall005a.png"
dest_files=["res://.godot/imported/internal_heights_computerwall005a.png-5b91d9ae1df21071a242586d3f4b6b59.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
