[gd_scene load_steps=3 format=3 uid="uid://cqfr01uk73ce5"]

[sub_resource type="GDScript" id="GDScript_v8qca"]
resource_name = "ssr_quality"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = {
	\"Disabled\": null,
	\"Low\": {
		\"roughnessQuality\": RenderingServer.ENV_SSR_ROUGHNESS_QUALITY_LOW,
		\"maxSteps\": 32,
		\"fadeIn\": 0.3,
		\"fadeOut\": 1.0
	},
	\"Medium\": {
		\"roughnessQuality\": RenderingServer.ENV_SSR_ROUGHNESS_QUALITY_MEDIUM,
		\"maxSteps\": 64,
		\"fadeIn\": 0.15,
		\"fadeOut\": 2.0
	},
	\"High\": {
		\"roughnessQuality\": RenderingServer.ENV_SSR_ROUGHNESS_QUALITY_HIGH,
		\"maxSteps\": 128,
		\"fadeIn\": 0.075,
		\"fadeOut\": 4.0
	}
}


# Called to apply the settings in the settings cache
func _apply_settings() -> void:
	apply_in_game_setting(OPTION_LIST_[currentValue])
	
	if currentValue == \"Disabled\":
		return
	
	RenderingServer.environment_set_ssr_roughness_quality(
		OPTION_LIST_[currentValue][\"roughnessQuality\"]
	)
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="SSRQuality" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
tooltip_text = "Screen Space Reflection"
script = SubResource("GDScript_v8qca")
DEFAULT_VALUE = "Disabled"
OptionsRef = NodePath("Options")
IDENTIFIER = "SSRQuality"
IS_IN_GAME_SETTING = true

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "SSR Quality"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
