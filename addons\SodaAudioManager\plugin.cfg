[plugin]

name="Soda AudioManager"
description="AudioManager is a global plugin for managing non-spatial sounds (such as UI, music, and the main character's sounds) in Godot. It allows playing sounds from files with a simple function, setting whether a music track loops or not, and globally controlling the volume of music and sound effects."
author="Alexsander <PERSON>(CyNoctis)"
version="1.1"
script="soda_audio_def.gd"
