[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://53discmcr7gn"
path="res://.godot/imported/hit_scan_2d.svg-76735babe2b0e36ac7e614925b1aeaa5.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://addons/health_hitbox_hurtbox/2d/hit_scan_2d/hit_scan_2d.svg"
dest_files=["res://.godot/imported/hit_scan_2d.svg-76735babe2b0e36ac7e614925b1aeaa5.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
svg/scale=1.0
editor/scale_with_editor_scale=false
editor/convert_colors_with_editor_theme=false
