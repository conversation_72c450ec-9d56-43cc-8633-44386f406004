# Attribution
## Collaborators

### Role
Person 1  
Person 2  
[Person w/ Link]()  

## Sourced
### Asset Type
#### Use Case
Author: [Name]()  
Source: [Domain : webpage.html]()  
License: [License]()

#### Godot Engine Logo
Author: <PERSON>  
Source: [godotengine.org : press](https://godotengine.org/press/)  
License: [CC BY 4.0 International](https://github.com/godotengine/godot/blob/master/LOGO_LICENSE.txt) 

## Tools
#### Godot
![Godot Engine Logo](/resource/core/ui/assets/godot_engine_logo/logo_vertical_color_dark.png)  
Author: [<PERSON>, <PERSON>, and contributors](https://godotengine.org/contact)  
Source: [godotengine.org](https://godotengine.org/)  
License: [MIT License](https://github.com/godotengine/godot/blob/master/LICENSE.txt) 

#### Godot Menus Template
![Maaack Plugin Icon](/resource/core/ui/assets/plugin_logo/logo.png)  
Author: [<PERSON><PERSON> and contributors](https://github.com/Maaack/Godot-Menus-Template/graphs/contributors)  
Source: [github: Godot-Menus-Template](https://github.com/Maaack/Godot-Menus-Template)  
License: [MIT License](LICENSE.txt)  

#### Git
![Git Logo](/resource/core/ui/assets/git_logo/Git-Logo-2Color.png)  
Author: [Linus Torvalds](https://github.com/torvalds)  
Source: [git-scm.com](https://git-scm.com/downloads)  
License: [GNU General Public License version 2](https://opensource.org/licenses/GPL-2.0)
