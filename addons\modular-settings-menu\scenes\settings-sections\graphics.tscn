[gd_scene load_steps=16 format=3 uid="uid://buqq0kms2dbb4"]

[ext_resource type="Script" uid="uid://d27da0yf24sse" path="res://addons/modular-settings-menu/scripts/settings_section.gd" id="1_704sp"]
[ext_resource type="PackedScene" uid="uid://byff0jwaicxvr" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/display_mode.tscn" id="1_mr6e7"]
[ext_resource type="PackedScene" uid="uid://cn8r63dmvd55c" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/resolution.tscn" id="2_5bpi0"]
[ext_resource type="PackedScene" uid="uid://b3w7qpn4nhmu2" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/scaler.tscn" id="4_g2t2s"]
[ext_resource type="PackedScene" uid="uid://dghfxwcbi5ig" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/max_fps.tscn" id="5_82fyi"]
[ext_resource type="PackedScene" uid="uid://idtbqnsqlvb6" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/vsync.tscn" id="6_2rh1a"]
[ext_resource type="PackedScene" uid="uid://csg6c3uuct1ls" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/anti_aliasing.tscn" id="7_r8hxv"]
[ext_resource type="PackedScene" uid="uid://3ckate3v614f" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/depth_of_field.tscn" id="8_28f8l"]
[ext_resource type="PackedScene" uid="uid://cgqiotob5aaoq" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/shadow_quality.tscn" id="9_1x78v"]
[ext_resource type="PackedScene" uid="uid://cqfr01uk73ce5" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/ssr_quality.tscn" id="10_cfuis"]
[ext_resource type="PackedScene" uid="uid://du5urp5d2dyjb" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/glow_quality.tscn" id="10_m0pim"]
[ext_resource type="PackedScene" uid="uid://bk5ky60jln7ag" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/ssao_quality.tscn" id="11_xa1gl"]
[ext_resource type="PackedScene" uid="uid://bmnvig4gfqj0c" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/ssil_quality.tscn" id="12_jqvyp"]
[ext_resource type="PackedScene" uid="uid://ddykljx6ndodi" path="res://addons/modular-settings-menu/scenes/settings-elements/graphics-elements/sdfgi_quality.tscn" id="13_ifeh4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_v0fou"]

[node name="Graphics" type="TabBar"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/tab_focus = SubResource("StyleBoxEmpty_v0fou")
script = ExtResource("1_704sp")
IDENTIFIER = "Graphics"

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer"]
layout_mode = 2
horizontal_scroll_mode = 0

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="ElementList" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 24

[node name="BasicSettings" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList"]
layout_mode = 2

[node name="SubSectionName" type="Label" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/BasicSettings"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Basic Settings"

[node name="HSeparator" type="HSeparator" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/BasicSettings"]
layout_mode = 2

[node name="SubSectionElements" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/BasicSettings"]
layout_mode = 2

[node name="DisplayMode" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/BasicSettings/SubSectionElements" instance=ExtResource("1_mr6e7")]
layout_mode = 2
defaultValue = "Windowed"

[node name="Resolution" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/BasicSettings/SubSectionElements" instance=ExtResource("2_5bpi0")]
layout_mode = 2
defaultValue = "1280x720"

[node name="MaxFPS" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/BasicSettings/SubSectionElements" instance=ExtResource("5_82fyi")]
layout_mode = 2

[node name="AdvancedSettings" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList"]
layout_mode = 2

[node name="SubSectionName" type="Label" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Advanced Settings
"

[node name="HSeparator" type="HSeparator" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings"]
layout_mode = 2

[node name="SubSectionElements" type="VBoxContainer" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings"]
layout_mode = 2

[node name="Scaler" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("4_g2t2s")]
layout_mode = 2

[node name="AntiAliasing" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("7_r8hxv")]
layout_mode = 2

[node name="VSync" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("6_2rh1a")]
layout_mode = 2

[node name="DepthOfField" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("8_28f8l")]
layout_mode = 2

[node name="ShadowQuality" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("9_1x78v")]
layout_mode = 2

[node name="GlowQuality" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("10_m0pim")]
layout_mode = 2

[node name="SSRQuality" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("10_cfuis")]
layout_mode = 2

[node name="SSAOQuality" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("11_xa1gl")]
layout_mode = 2

[node name="SSILQuality" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("12_jqvyp")]
layout_mode = 2

[node name="SDFGIQuality" parent="MarginContainer/ScrollContainer/MarginContainer/ElementList/AdvancedSettings/SubSectionElements" instance=ExtResource("13_ifeh4")]
layout_mode = 2
