[gd_scene load_steps=9 format=3 uid="uid://c6k5nnpbypshi"]

[ext_resource type="Script" uid="uid://bhgs1upaahk3y" path="res://addons/maaacks_menus_template/base/scenes/menus/main_menu/main_menu.gd" id="1"]
[ext_resource type="PackedScene" uid="uid://hmx6o472ropw" path="res://addons/maaacks_menus_template/base/scenes/menus/options_menu/master_options_menu_with_tabs.tscn" id="2_qvrrd"]
[ext_resource type="PackedScene" uid="uid://osxulxw2oas3" path="res://addons/maaacks_menus_template/base/scenes/credits/scrollable_credits.tscn" id="3_73am8"]
[ext_resource type="Script" uid="uid://1nf36h0gms3q" path="res://addons/maaacks_menus_template/base/scripts/capture_focus.gd" id="4_l1ebe"]
[ext_resource type="PackedScene" uid="uid://bkcsjsk2ciff" path="res://addons/maaacks_menus_template/base/scenes/music_players/background_music_player.tscn" id="4_w8sbm"]
[ext_resource type="Script" uid="uid://b5oej1q4h7jvh" path="res://addons/maaacks_menus_template/base/scripts/ui_sound_controller.gd" id="6_bs342"]
[ext_resource type="Script" uid="uid://dmkubt2nsnsbn" path="res://addons/maaacks_menus_template/base/scenes/menus/main_menu/config_version_label.gd" id="6_pdiij"]
[ext_resource type="Script" uid="uid://bkwlopi4qn32o" path="res://addons/maaacks_menus_template/base/scenes/menus/main_menu/config_name_label.gd" id="7_j7612"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1")
options_packed_scene = ExtResource("2_qvrrd")
credits_packed_scene = ExtResource("3_73am8")

[node name="UISoundController" type="Node" parent="."]
script = ExtResource("6_bs342")

[node name="BackgroundMusicPlayer" parent="." instance=ExtResource("4_w8sbm")]

[node name="BackgroundTextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
expand_mode = 1
stretch_mode = 5

[node name="VersionMargin" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="VersionContainer" type="Control" parent="VersionMargin"]
layout_mode = 2
mouse_filter = 2

[node name="VersionLabel" type="Label" parent="VersionMargin/VersionContainer"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -88.0
offset_top = -26.0
grow_horizontal = 0
grow_vertical = 0
text = "v0.0.0"
horizontal_alignment = 2
script = ExtResource("6_pdiij")

[node name="MenuContainer" type="MarginContainer" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleMargin" type="MarginContainer" parent="MenuContainer"]
layout_mode = 2
theme_override_constants/margin_top = 24

[node name="TitleContainer" type="Control" parent="MenuContainer/TitleMargin"]
layout_mode = 2
mouse_filter = 2

[node name="TitleLabel" type="Label" parent="MenuContainer/TitleMargin/TitleContainer"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 67.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 48
text = "Title"
horizontal_alignment = 1
vertical_alignment = 1
script = ExtResource("7_j7612")

[node name="SubTitleMargin" type="MarginContainer" parent="MenuContainer"]
layout_mode = 2
theme_override_constants/margin_top = 92

[node name="SubTitleContainer" type="Control" parent="MenuContainer/SubTitleMargin"]
layout_mode = 2
mouse_filter = 2

[node name="SubTitleLabel" type="Label" parent="MenuContainer/SubTitleMargin/SubTitleContainer"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 34.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 24
text = "Subtitle"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MenuButtonsMargin" type="MarginContainer" parent="MenuContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/margin_top = 136
theme_override_constants/margin_bottom = 8

[node name="MenuButtonsContainer" type="Control" parent="MenuContainer/MenuButtonsMargin"]
layout_mode = 2
mouse_filter = 2

[node name="MenuButtonsBoxContainer" type="BoxContainer" parent="MenuContainer/MenuButtonsMargin/MenuButtonsContainer"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -64.0
offset_top = -104.0
offset_right = 64.0
offset_bottom = 104.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_constants/separation = 16
alignment = 1
vertical = true
script = ExtResource("4_l1ebe")

[node name="NewGameButton" type="Button" parent="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "New Game"

[node name="OptionsButton" type="Button" parent="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Options"

[node name="CreditsButton" type="Button" parent="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Credits"

[node name="ExitButton" type="Button" parent="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Exit"

[node name="OptionsContainer" type="MarginContainer" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="CreditsContainer" type="MarginContainer" parent="."]
unique_name_in_owner = true
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
theme_override_constants/margin_left = 32
theme_override_constants/margin_top = 32
theme_override_constants/margin_right = 32
theme_override_constants/margin_bottom = 64

[node name="FlowControlContainer" type="MarginContainer" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
theme_override_constants/margin_left = 16
theme_override_constants/margin_top = 16
theme_override_constants/margin_right = 16
theme_override_constants/margin_bottom = 16

[node name="FlowControl" type="Control" parent="FlowControlContainer"]
layout_mode = 2
mouse_filter = 2

[node name="BackButton" type="Button" parent="FlowControlContainer/FlowControl"]
unique_name_in_owner = true
visible = false
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -31.0
offset_right = 45.0
grow_vertical = 0
text = "Back"

[connection signal="pressed" from="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer/NewGameButton" to="." method="_on_new_game_button_pressed"]
[connection signal="pressed" from="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer/OptionsButton" to="." method="_on_options_button_pressed"]
[connection signal="pressed" from="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer/CreditsButton" to="." method="_on_credits_button_pressed"]
[connection signal="pressed" from="MenuContainer/MenuButtonsMargin/MenuButtonsContainer/MenuButtonsBoxContainer/ExitButton" to="." method="_on_exit_button_pressed"]
[connection signal="pressed" from="FlowControlContainer/FlowControl/BackButton" to="." method="_on_back_button_pressed"]
