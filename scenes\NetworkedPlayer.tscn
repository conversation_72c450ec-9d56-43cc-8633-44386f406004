[gd_scene load_steps=3 format=3 uid="uid://b8qwu6x5p2mjn"]

[ext_resource type="Script" uid="uid://dlyr6tcp7n6ol" path="res://scripts/player/NetworkedPlayer.gd" id="1_3k2jm"]

[sub_resource type="SceneReplicationConfig" id="SceneReplicationConfig_1"]
properties/0/path = NodePath(".:global_position")
properties/0/spawn = true
properties/0/replication_mode = 1
properties/1/path = NodePath(".:global_rotation")
properties/1/spawn = true
properties/1/replication_mode = 1
properties/2/path = NodePath(".:health")
properties/2/spawn = true
properties/2/replication_mode = 1
properties/3/path = NodePath(".:is_alive")
properties/3/spawn = true
properties/3/replication_mode = 1

[node name="NetworkedPlayer" type="CharacterBody3D"]
script = ExtResource("1_3k2jm")

[node name="MultiplayerSynchronizer" type="MultiplayerSynchronizer" parent="."]
replication_config = SubResource("SceneReplicationConfig_1")
