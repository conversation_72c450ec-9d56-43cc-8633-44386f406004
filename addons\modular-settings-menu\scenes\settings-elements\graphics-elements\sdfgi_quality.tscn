[gd_scene load_steps=3 format=3 uid="uid://ddykljx6ndodi"]

[sub_resource type="GDScript" id="GDScript_6k8bu"]
resource_name = "sdfgi_quality"
script/source = "extends OptionElement


func _init() -> void:
	OPTION_LIST_ = {
		\"Disabled\": null,
		\"Low\": RenderingServer.ENV_SDFGI_RAY_COUNT_8,
		\"Medium\": RenderingServer.ENV_SDFGI_RAY_COUNT_32,
		\"High\": RenderingServer.ENV_SDFGI_RAY_COUNT_96
	}


# Called to apply the settings in the settings cache
func apply_settings() -> void:
	apply_in_game_setting(currentValue)
	
	if currentValue == \"Disabled\":
		return
	
	RenderingServer.environment_set_sdfgi_ray_count(OPTION_LIST_[currentValue])
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="SDFGIQuality" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
tooltip_text = "Signed Distance Field Global Illumination"
script = SubResource("GDScript_6k8bu")
DEFAULT_VALUE = "Disabled"
OptionsRef = NodePath("Options")
IDENTIFIER = "SDFGIQuality"
IS_IN_GAME_SETTING = true

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "SDFGI Quality"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
