[gd_scene load_steps=3 format=3 uid="uid://cxaxyqer0af21"]

[sub_resource type="GDScript" id="GDScript_bpxmf"]
resource_name = "toggle_element_template"
script/source = "extends ToggleElement


# Called to apply the setting to the game
func _apply_settings() -> void:
	pass
"

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_drjjf"]

[node name="ToggleElementTemplate" type="HBoxContainer" node_paths=PackedStringArray("ToggleRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_bpxmf")
ToggleRef = NodePath("Toggle")
IDENTIFIER = "ToggleTemplate"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Toggle Element"
vertical_alignment = 1

[node name="Toggle" type="CheckButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_drjjf")
