[gd_scene load_steps=2 format=3 uid="uid://cn8r63dmvd55c"]

[sub_resource type="GDScript" id="GDScript_utf5l"]
resource_name = "resolution"
script/source = "extends OptionElement

## Toggle window resizing by the user.
@export var RESIZABLE: bool = false


func _init() -> void:
	OPTION_LIST_ = {
		\"3840x2160\": Vector2i(3840, 2160),
		\"2560x1440\": Vector2i(2560, 1440),
		\"1920x1080\": Vector2i(1920, 1080),
		\"1280x720\": Vector2i(1280, 720),
		\"960x540\": Vector2i(960, 540)
	}


func _ready() -> void:
	super._ready()
	# Toggle window resizing
	DisplayServer.window_set_flag(
		DisplayServer.WINDOW_FLAG_RESIZE_DISABLED,
		!RESIZABLE
	)


func _apply_settings() -> void:
	if DisplayServer.window_get_mode() == DisplayServer.WINDOW_MODE_EXCLUSIVE_FULLSCREEN:
		return
	
	if DisplayServer.window_get_flag(DisplayServer.WINDOW_FLAG_BORDERLESS):
		return
	
	# Change the window size to the selected resolution
	get_window().set_size(OPTION_LIST_[currentValue])
	get_viewport().set_size(OPTION_LIST_[currentValue])
	get_window().move_to_center()
"

[node name="Resolution" type="HBoxContainer" node_paths=PackedStringArray("OptionsRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = SubResource("GDScript_utf5l")
DEFAULT_VALUE = "1920x1080"
OptionsRef = NodePath("Options")
IDENTIFIER = "Resolution"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Resolution"
vertical_alignment = 1

[node name="Options" type="OptionButton" parent="."]
layout_mode = 2
size_flags_horizontal = 3
focus_mode = 0
