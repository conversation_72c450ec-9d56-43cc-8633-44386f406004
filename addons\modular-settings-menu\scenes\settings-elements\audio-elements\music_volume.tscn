[gd_scene load_steps=4 format=3 uid="uid://y6kuxwyem48c"]

[ext_resource type="Script" uid="uid://dnil4q0opnnt2" path="res://addons/modular-settings-menu/scripts/settings-elements-scripts/audio_setting.gd" id="1_05os8"]

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_hlot4"]
size = Vector2(0, 0)

[sub_resource type="Theme" id="Theme_4i2xw"]
SpinBox/icons/updown = SubResource("PlaceholderTexture2D_hlot4")

[node name="MusicVolume" type="HBoxContainer" node_paths=PackedStringArray("SliderRef", "ValueBoxRef")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_05os8")
AUDIO_BUS = "Music"
STEP_VALUE = 0.01
DISPLAY_PERCENT_VALUE = true
VALUE_SUFFIX = "%"
SliderRef = NodePath("SliderValue/Slider")
ValueBoxRef = NodePath("SliderValue/Value")
IDENTIFIER = "MusicVolume"

[node name="Label" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Music"
vertical_alignment = 1

[node name="SliderValue" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 6

[node name="Slider" type="HSlider" parent="SliderValue"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
max_value = 0.0
step = 0.0
ticks_on_borders = true

[node name="Value" type="SpinBox" parent="SliderValue"]
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 4
theme = SubResource("Theme_4i2xw")
max_value = 0.0
step = 0.0
alignment = 2
