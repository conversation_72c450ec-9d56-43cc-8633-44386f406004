[configuration]

entry_symbol = "sqlite_library_init"
compatibility_minimum = "4.3"

[libraries]

macos.debug = "res://addons/playnub/gdsqlite/libgdsqlite.macos.template_debug.framework"
macos.release = "res://addons/playnub/gdsqlite/libgdsqlite.macos.template_release.framework"
windows.debug.x86_64 = "res://addons/playnub/gdsqlite/libgdsqlite.windows.template_debug.x86_64.dll"
windows.release.x86_64 = "res://addons/playnub/gdsqlite/libgdsqlite.windows.template_release.x86_64.dll"
linux.debug.x86_64 = "res://addons/playnub/gdsqlite/libgdsqlite.linux.template_debug.x86_64.so"
linux.release.x86_64 = "res://addons/playnub/gdsqlite/libgdsqlite.linux.template_release.x86_64.so"
android.debug.arm64 = "res://addons/playnub/gdsqlite/libgdsqlite.android.template_debug.arm64.so"
android.release.arm64 = "res://addons/playnub/gdsqlite/libgdsqlite.android.template_release.arm64.so"
android.debug.x86_64 = "res://addons/playnub/gdsqlite/libgdsqlite.android.template_debug.x86_64.so"
android.release.x86_64 = "res://addons/playnub/gdsqlite/libgdsqlite.android.template_release.x86_64.so"
ios.debug = "res://addons/playnub/gdsqlite/libgdsqlite.ios.template_debug.xcframework"
ios.release = "res://addons/playnub/gdsqlite/libgdsqlite.ios.template_release.xcframework"
web.debug.wasm32 = "res://addons/playnub/gdsqlite/libgdsqlite.web.template_debug.wasm32.wasm"
web.release.wasm32 = "res://addons/playnub/gdsqlite/libgdsqlite.web.template_release.wasm32.wasm"

[dependencies]

ios.debug = { 
    "res://addons/playnub/gdsqlite/libgodot-cpp.ios.template_debug.xcframework": "" 
}
ios.release = { 
    "res://addons/playnub/gdsqlite/libgodot-cpp.ios.template_release.xcframework": "" 
}